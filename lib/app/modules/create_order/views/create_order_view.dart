import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:guests/app/components/background.dart';
import 'package:guests/app/components/dialog_custom.dart';
import 'package:guests/app/components/future_progress.dart';
import 'package:guests/app/components/invoice_page.dart';
import 'package:guests/app/components/BottomButton.dart';
import 'package:guests/constants.dart';
import 'package:guests/enums.dart';
import 'package:guests/extension.dart';
import 'package:screenshot/screenshot.dart';

import '../controllers/create_order_controller.dart';

class CreateOrderView extends GetView<CreateOrderController> {
  final _formKey = GlobalKey<FormState>();

  CreateOrderView({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('OKpay'),
      ),
      body: ClipRRect(
        borderRadius: kTopRadius,
        child: Stack(
          alignment: Alignment.topCenter,
          children: _children().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    // 發票截圖
    yield Obx(() {
      if (controller.invoiceVisible) {
        final ret = Screenshot(
          controller: controller.screenshotController,
          child: InvoicePage(
            controller.orderDetail.toInvoice(),
            storeName: controller.prefProvider.storeName,
            productName: controller.itemName.value,
          ),
        );
        controller.widgetUpdater.complete();
        return ret;
      }
      return const SizedBox.shrink();
    });
    // 國防布
    yield ColoredBox(
      color: kColorBackground,
      child: SizedBox.expand(),
    );
    // 使用者實際看到的預覽介面
    yield _body();
  }

  Widget _body() {
    return Background(
      background: Form(
        key: _formKey,
        child: _page(),
      ),
      child: Align(
        alignment: Alignment.bottomCenter,
        child: BottomButton(
          '新增消費記錄',
          onPressed: _submit,
        ),
      ),
    );
  }

  Future<void> _submit() async {
    if (_formKey.currentState?.validate() == true) {
      bool needToProcess = true;
      if (controller.invoiceEnabled && controller.draft.total! <= 0) {
        final button = await DialogCustom.showConfirm(
          titleText: '不開立發票',
          contentText: '結帳金額為0，不開立發票',
          rightButtonText: '繼續結帳',
        );
        if (button == null || button.isNegative) {
          needToProcess = false;
        }
      }
      if (needToProcess) {
        final orderId = await FutureProgress.show(
          future: controller.submit(),
        );
        if (orderId is num && orderId > 0) {
          Get.back(result: orderId);
        }
      }
    }
  }

  Widget _page() {
    return ListView(
      padding: EdgeInsets.only(bottom: kBottomPadding),
      children: _pageChildren().toList(growable: false),
    );
  }

  Iterable<Widget> _pageChildren() sync* {
    yield const SizedBox(height: 18.0);
    yield _Editor(
      // initText: '芒果禮盒',
      initText: controller.itemName.value,
      inputFormatters: [
        FilteringTextInputFormatter.singleLineFormatter,
      ],
      hintText: '商品名稱',
      onChanged: controller.itemName,
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 4.0,
    );
    yield _ListTile(
      leading: _IconLabel(
        icon: 'assets/images/icon_pos_01.svg',
        text: '商品小計',
      ),
      trailing: _Editor(
        textAlign: TextAlign.end,
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          FilteringTextInputFormatter.digitsOnly,
        ],
        keyboardType: TextInputType.number,
        onChanged: (value) {
          final _ = num.tryParse(value) ?? 0;
          controller.draft.subtotal = _;
          controller.refreshDraft();
        },
      ),
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 4.0,
    );
    yield Divider(
      indent: kPadding,
      endIndent: kPadding,
    );
    yield _ListTile(
      leading: _IconLabel(
        icon: 'assets/images/icon_pos_03.svg',
        text: '現場折價',
      ),
      title: Text(
        '－',
        style: TextStyle(
          fontSize: 16,
          color: const Color(0xff333333),
        ),
        textAlign: TextAlign.center,
      ).paddingSymmetric(
        horizontal: kDefaultPadding,
      ),
      trailing: _Editor(
        textAlign: TextAlign.end,
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          FilteringTextInputFormatter.digitsOnly,
        ],
        keyboardType: TextInputType.number,
        onChanged: (value) {
          final _ = num.tryParse(value) ?? 0;
          controller.draft.discount = _;
          controller.refreshDraft();
        },
      ),
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 4.0,
    );
    yield Divider(
      indent: kPadding,
      endIndent: kPadding,
    );
    yield _ListTile(
      leading: _IconLabel(
        icon: 'assets/images/icon_pos_04.svg',
        text: '額外費用',
      ),
      title: Text(
        '＋',
        style: TextStyle(
          fontSize: 16,
          color: const Color(0xff333333),
        ),
        textAlign: TextAlign.center,
      ).paddingSymmetric(
        horizontal: kDefaultPadding,
      ),
      trailing: _Editor(
        textAlign: TextAlign.end,
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          FilteringTextInputFormatter.digitsOnly,
        ],
        keyboardType: TextInputType.number,
        onChanged: (value) {
          final _ = num.tryParse(value) ?? 0;
          controller.draft.additionalCharges = _;
          controller.refreshDraft();
        },
      ),
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 4.0,
    );
    yield Divider(
      indent: kPadding,
      endIndent: kPadding,
    );
    yield _ListTile(
      leading: _IconLabel(
        icon: 'assets/images/icon_pos_05.svg',
        text: '商品總價',
      ),
      trailing: Obx(() {
        return Text(
          // '\$1670',
          '\$${controller.displayTotal}',
          style: TextStyle(
            fontSize: 32,
            color: const Color(0xd9000000),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.right,
        );
      }),
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 4.0,
    );
    yield _ListTile(
      leading: _IconLabel(
        icon: 'assets/images/icon_pos_06.svg',
        text: '實收',
      ),
      trailing: _Editor(
        textAlign: TextAlign.end,
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
          FilteringTextInputFormatter.digitsOnly,
        ],
        // controller: controller.paidController,
        keyboardType: TextInputType.number,
        onChanged: (value) {
          final _ = num.tryParse(value) ?? 0;
          controller.draft.paid = _;
          controller.refreshDraft();
        },
      ),
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 8.0,
    );
    yield _ListTile(
      leading: _IconLabel(
        icon: 'assets/images/icon_pos_07.svg',
        text: '找零',
      ),
      trailing: Obx(() {
        return Text(
          // '\$30',
          '\$${controller.draft.displayChange}',
          style: const TextStyle(
            fontSize: 32,
            color: const Color(0xffe02020),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.right,
        );
      }),
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 4.0,
    );
    if (controller.prefProvider.invoiceEnabled) {
      yield ColoredBox(
        color: const Color(0xffdedee6),
        child: _buyerWidget(),
      );
    }
  }

  Widget _buyerWidget() {
    Iterable<Widget> children() sync* {
      yield Expanded(
        child: Obx(() {
          return _Editor(
            enabled: controller.invoiceEnabled,
            inputFormatters: [
              FilteringTextInputFormatter.singleLineFormatter,
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(kCountInvoice),
            ],
            onChanged: (value) {
              // 統一編號
              if (GetUtils.hasMatch(value, kPatternInvoice)) {
                controller.draft.vatNumber = value;
              }
            },
            hintText: '買方統一編號',
            keyboardType: TextInputType.number,
            validator: (value) {
              final _ = value ?? '';
              if (_.isEmpty) {
                return null;
              }
              if (GetUtils.hasMatch(_, kPatternInvoice)) {
                return null;
              }
              if (controller.invoiceEnabled == false) {
                return null;
              }
              return '請輸入8碼數字';
            },
          );
        }),
      );
      yield const SizedBox(width: 12.0);
      yield Container(
        height: 54.0,
        alignment: Alignment.center,
        child: Text(
          '免開發票',
          style: TextStyle(
            fontSize: 14,
            color: const Color(0xff6d7278),
          ),
          textAlign: TextAlign.right,
        ),
      );
      yield const SizedBox(width: 8.0);
      yield Obx(() {
        return Container(
          height: 54.0,
          alignment: Alignment.center,
          child: Switch(
            value: !(controller.invoiceEnabled),
            onChanged: (bool value) {
              controller.invoiceEnabled = !value;
            },
          ),
        );
      });
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children().toList(growable: false),
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 20.0,
    );
  }
}

class _ListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? trailing;

  const _ListTile({
    Key? key,
    this.leading,
    this.title,
    this.trailing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield Expanded(
      child: Row(
        children: [
          Expanded(
            child: leading ?? const SizedBox(),
          ),
          title ?? const SizedBox(),
        ],
      ),
    );
    yield Expanded(
      child: trailing ?? const SizedBox(),
    );
  }
}

class _IconLabel extends StatelessWidget {
  final String? icon;
  final String? text;

  const _IconLabel({
    Key? key,
    this.icon,
    this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield SvgPicture.asset(icon ?? '');
    yield const SizedBox(width: 12.0);
    yield Text(
      // '商品小計',
      text ?? '',
      style: const TextStyle(
        fontSize: 14,
        color: const Color(0xff6d7278),
      ),
      textAlign: TextAlign.left,
    );
  }
}

// TODO: add max length property
class _Editor extends StatelessWidget {
  final String? hintText;
  final String? initText;
  final TextInputType? keyboardType;
  final FormFieldValidator<String>? validator;
  final ValueChanged<String>? onChanged;
  final TextEditingController? controller;
  final List<TextInputFormatter>? inputFormatters;
  final TextAlign? textAlign;
  final bool? readonly;
  final bool? enabled;

  const _Editor({
    Key? key,
    this.textAlign,
    this.hintText,
    this.initText,
    this.validator,
    this.onChanged,
    this.controller,
    this.readonly,
    this.enabled,
    this.keyboardType,
    this.inputFormatters,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      initialValue: initText ?? '',
      enabled: enabled ?? true,
      readOnly: readonly ?? false,
      textAlign: textAlign ?? TextAlign.start,
      inputFormatters: inputFormatters,
      controller: controller,
      onChanged: onChanged,
      validator: validator,
      keyboardType: keyboardType ?? TextInputType.text,
      style: const TextStyle(
        fontSize: 24,
        color: Colors.black,
      ),
      decoration: InputDecoration(
        contentPadding: EdgeInsets.symmetric(
          horizontal: kPadding,
          vertical: 12.0,
        ),
        fillColor: Colors.white,
        filled: true,
        isDense: true,
        hintMaxLines: 1,
        hintStyle: const TextStyle(
          fontSize: 24,
          color: const Color(0xffbfbfbf),
        ),
        hintText: hintText ?? '',
        border: OutlineInputBorder(
          borderSide: BorderSide(
            width: 1.0,
            color: const Color(0xffdddddd),
          ),
          borderRadius: const BorderRadius.all(const Radius.circular(10.0)),
        ),
      ),
    );
  }
}
