import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/background.dart';
import 'package:guests/app/components/dialog_actions.dart';
import 'package:guests/app/modules/invoice_settings/controllers/invoice_settings_controller.dart';
import 'package:guests/app/components/BottomButton.dart';
import 'package:guests/app/components/text_tile.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';
import 'package:guests/ok_colors.dart';
import 'package:guests/enums.dart';

class InvoiceSettingsView extends GetView<InvoiceSettingsController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('發票設定'),
      ),
      body: DecoratedBox(
        decoration: const BoxDecoration(
          borderRadius: kTopRadius,
          color: OkColors.background,
        ),
        child: controller.obx(
          (state) {
            return Background(
              background: _body(),
              child: Align(
                alignment: Alignment.bottomCenter,
                child: BottomButton(
                  '儲存',
                  onPressed: _submit,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Future<void> _submit() async {
    Get.showLoading();
    try {
      await this.controller.submit();
      Get.back();
      Get.back();
    } catch (e) {
      Get.showAlert(e.toString());
    }
  }

  Widget _body() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield Padding(
      padding: EdgeInsets.symmetric(vertical: kPadding),
      child: Text(
        '請填寫發票資訊',
        style: TextStyle(
          fontSize: 16,
          color: kColorPrimary,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ),
    );
    yield Expanded(
      child: SingleChildScrollView(
        child: Obx(() => _page()),
      ),
    );
  }

  Widget _page() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _pageChildren().toList(growable: false),
    );
  }

  Iterable<Widget> _pageChildren() sync* {
    yield Container(
      color: Colors.white,
      child: SwitchListTile(
        title: Text('啟用電子發票功能'),
        value: controller.draft.invoiceEnabled ?? false,
        onChanged: (value) {
          controller.draft.invoiceEnabled = value;
          controller.refreshDraft();
        },
      ),
    );
    if (controller.draft.invoiceEnabled ?? false) {
      yield _subPage();
    }
  }

  Widget _subPage() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _subPageChildren().toList(growable: false),
    );
  }

  Iterable<Widget> _subPageChildren() sync* {
    yield TextTile.header('商家資訊');
    yield ColoredBox(
      color: Colors.white,
      child: ListTile(
        leading: Text('統一編號'),
        title: Text(
          controller.prefProvider.taxId,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black,
          ),
        ),
      ),
    );
    yield const SizedBox(height: 1.0);
    yield ColoredBox(
      color: Colors.white,
      child: ListTile(
        title: TextFormField(
          initialValue: controller.draft.itemName,
          onChanged: (value) => controller.draft.itemName = value,
          decoration: InputDecoration(
            contentPadding: EdgeInsets.zero,
            labelText: '預設商品名稱',
            hintText: '請輸入預設商品名稱',
          ),
        ),
      ),
    );
    yield TextTile.header('加值中心');
    yield ColoredBox(
      color: Colors.white,
      child: TextTile.expand('金財通', onPressed: () {
        DialogActions.show(
          titleText: '請選擇加值中心',
          actions: [
            '金財通',
          ],
        );
      }),
    );
    yield TextTile.header('稅率');
    final taxType = ExtensionTaxType.fromValue(
            controller.draft.taxType ?? TaxType.Taxable.value) ??
        TaxType.Taxable;
    yield ColoredBox(
      color: Colors.white,
      child: TextTile.expand(
        taxType.displayName,
        onPressed: this._showDialog,
      ),
    );
    yield const SizedBox(height: 1.0);
    yield ColoredBox(
      color: Colors.white,
      child: SwitchListTile(
        title: Text('單筆訂單免開發票'),
        value: controller.draft.invoiceSkipped ?? false,
        onChanged: (value) {
          controller.draft.invoiceSkipped = value;
          controller.refreshDraft();
        },
      ),
    );
  }

  Future<void> _showDialog() async {
    final taxTypes = TaxType.values;
    final index = await DialogActions.show<num>(
      titleText: '選擇稅率',
      actions: taxTypes.map((taxType) => taxType.displayName).toList(),
    );
    if (index != null && index >= 0 && index < taxTypes.length) {
      final selectedTaxType = taxTypes[index.toInt()];
      controller.draft.taxType = selectedTaxType.value;
      controller.refreshDraft();
    }
  }
}
