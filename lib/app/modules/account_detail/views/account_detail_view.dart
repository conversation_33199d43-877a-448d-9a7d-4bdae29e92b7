import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/background.dart';
import 'package:guests/app/components/blank.dart';
import 'package:guests/app/components/dialog_actions.dart';

import 'package:guests/app/modules/account_detail/controllers/account_detail_controller.dart';
import 'package:guests/app/components/BottomButton.dart';
import 'package:guests/app/components/custom_editor.dart';
import 'package:guests/constants.dart';
import 'package:guests/enums.dart';
import 'package:guests/extension.dart';
import 'package:guests/ok_colors.dart';

class AccountDetailView extends GetView<AccountDetailController> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(controller.displayTitle),
      ),
      body: DecoratedBox(
        decoration: const BoxDecoration(
          borderRadius: kTopRadius,
          color: OkColors.background,
        ),
        child: controller.obx(
          (state) {
            return Background(
              background: Obx(() => _body()),
              child: Align(
                alignment: Alignment.bottomCenter,
                child: BottomButton(
                  controller.displayButton,
                  onPressed: _submit,
                ),
              ),
            );
          },
          onError: (error) => Blank(),
        ),
      ),
    );
  }

  Future<void> _submit() async {
    // 在 View 層進行表單驗證
    final state = _formKey.currentState;
    if (state != null && state.validate()) {
      // 驗證通過後，調用 Controller 的業務邏輯
      Get.showLoading();
      try {
        await controller.submit();
        Get.back();
        Get.back();
      } catch (e) {
        Get.back();
        Get.showAlert(e.toString());
      }
    }
  }

  Iterable<Widget> _children() sync* {
    // 編輯帳號才需要顯示啟用帳號
    if (controller.isUpdating) {
      yield ColoredBox(
        color: Colors.white,
        child: SwitchListTile.adaptive(
          contentPadding: kContentPadding,
          title: Text(
            '啟用帳號', // TODO: i18n
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black,
            ),
            textAlign: TextAlign.left,
          ),
          value: controller.data.enabled,
          onChanged: (value) {
            controller.data.enabled = value;
            controller.refreshData();
          },
        ),
      );
    }
    if (controller.data.enabled) {
      yield* _page1();
      yield* _page3();
      yield* _page4();
    }
  }

  Widget _body() {
    Iterable<Widget> children() sync* {
      yield Text(
        '請填寫操作員資料', // TODO: i18n
        style: const TextStyle(
          fontSize: 16,
          color: kColorPrimary,
          fontWeight: FontWeight.w700,
        ),
        textAlign: TextAlign.center,
      ).paddingSymmetric(
        vertical: 12.0,
      );
      yield Expanded(
        child: Form(
          key: _formKey,
          child: ListView(
            padding: EdgeInsets.only(bottom: kBottomPadding),
            children: _children().toList(growable: false),
          ),
        ),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: children().toList(growable: false),
    );
  }

  Iterable<Widget> _page1() sync* {
    yield Text(
      '帳號資訊', // TODO: i18n
      style: const TextStyle(
        fontSize: 14,
        color: const Color(0xff666666),
      ),
      textAlign: TextAlign.left,
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 8.0,
    );
    yield CustomEditor(
      initialValue: controller.data.name,
      labelText: '名稱', // TODO: i18n
      hintText: '請輸入操作員名稱', // TODO: i18n
      onChanged: (value) {
        controller.data.name = value;
      },
    );
    yield CustomEditor(
      // readonly: this.controller.isUpdating,
      enable: this.controller.isCreating,
      initialValue: controller.data.username,
      labelText: '帳號', // TODO: i18n
      hintText: '請輸入帳號', // TODO: i18n
      onChanged: (value) {
        controller.data.username = value;
      },
      validator: (value) {
        value ??= '';
        if (value.isEmpty) {
          return '必填項目';
        }
        return null;
      },
    );
    yield CustomEditor(
      obscureText: true,
      labelText: '密碼', // TODO: i18n
      hintText: '請輸入密碼', // TODO: i18n
      onChanged: (value) {
        controller.password.value = value;
      },
      validator: (value) {
        value ??= '';
        if (value.isEmpty && controller.isCreating) {
          return '必填項目';
        }
        return null;
      },
    );
    yield CustomEditor(
      obscureText: true,
      labelText: '再次輸入密碼', // TODO: i18n
      hintText: '請輸入密碼', // TODO: i18n
      onChanged: (value) {
        controller.confirmPassword.value = value;
      },
      validator: (value) {
        if (this.controller.password.value !=
            this.controller.confirmPassword.value) {
          return '確認密碼不同'; // TODO: i18n
        }
        return null;
      },
    );
    yield Container(
      color: Colors.white,
      height: 8.0,
    );
  }

  Iterable<Widget> _page3() sync* {
    yield Text(
      '身份',
      style: const TextStyle(
        fontSize: 14,
        color: const Color(0xff666666),
      ),
      textAlign: TextAlign.left,
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 8.0,
    );
    yield ListTile(
      onTap: _showDialog,
      tileColor: Colors.white,
      title: Text(
        controller.data.displayRoleId,
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black,
        ),
        textAlign: TextAlign.left,
      ),
      trailing: const IconButton(
        icon: Icon(Icons.expand_more),
        onPressed: null,
      ),
    );
  }

  ///
  /// 選擇身份
  ///
  Future<void> _showDialog() async {
    final index = await DialogActions.show<num>(
      titleText: '請選擇身份', // TODO: i18n
      actions: <String>[
        StoreRole.Boss.name,
        StoreRole.Employee.name,
      ],
    );
    if (index == 0) {
      controller.data.roleId = StoreRole.Boss.index;
      controller.refreshData();
    } else if (index == 1) {
      controller.data.roleId = StoreRole.Employee.index;
      controller.refreshData();
    }
  }

  Iterable<Widget> _page4() sync* {
    yield Text(
      '備註', // TODO: i18n
      style: const TextStyle(
        fontSize: 14,
        color: const Color(0xff666666),
      ),
      textAlign: TextAlign.left,
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 8.0,
    );
    yield ColoredBox(
      color: Colors.white,
      child: TextFormField(
        initialValue: controller.data.comment,
        onChanged: (value) {
          controller.data.comment = value;
        },
        minLines: 3,
        keyboardType: TextInputType.multiline,
        maxLines: null,
        decoration: const InputDecoration(
          hintText: '請輸入備註…', // TODO: i18n
          border: const OutlineInputBorder(
            borderRadius: BorderRadius.zero,
          ),
          hintStyle: const TextStyle(
            fontSize: 16,
            color: const Color(0xff666666),
          ),
        ),
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 16.0,
      ),
    );
  }
}
